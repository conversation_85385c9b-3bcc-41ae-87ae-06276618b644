<script lang="ts" setup>
import { useQuery } from '@tanstack/vue-query';
import { point } from '@turf/helpers';
import { useHuntingStop } from '@composables';
import { useMapStore, useUserStore, useBAStore } from '@stores';
import { closeNotify, errorNotify, successNotify } from '@helpers';
import { BRAND_ACTION, RMI, USER } from '@repositories';
import { delay, useMotionTracking, useTrackData } from '@composables';
import {
  OngoingFreeCoinLayer,
  FoundCoinLayer,
  ForfeitedCoinLayer,
  ListenerCoinLayer,
  TimeUpCoinLayer,
  VerifyingCoinLayer,
  OngoingPaidCoinLayer,
  FoundPopUp,
  PaidPopUp,
  VerifyingPopUp,
  ForfeitedPopUp,
  TimeUpPopUp,
  BoundaryLayer,
  BeaconPopUp,
  DevToolLocation,
  LocationBasedMission,
  BeaconLayer,
  CoinSonarPopUp,
  PivotLayer,
  CoinSonarLayer,
} from '@components';
import type {
  GeolocateSuccess,
  LngLatLike,
  MapMouseEvent,
  MapOptions,
  Event,
  MapGeoJSONFeature,
  SymbolLayerStyle,
} from 'vue3-maplibre-gl';
import type {
  IAPIResponseError,
  ICircle,
  IPayloadTrackUserLocation,
  ISilverCoin,
  IWinnerInfo,
  TCoinStatus,
} from '@types';
import { isDifferentDay } from '@helpers';
import {
  Mapbox,
  GeolocateControls,
  MapCreationStatus,
  useMapbox,
  Image,
  LngLat,
  useFlyTo,
} from 'vue3-maplibre-gl';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import 'vue3-maplibre-gl/dist/style.css';
import { Loading } from 'quasar';
import { last } from 'lodash';
import { throttle } from 'lodash';

const storeMap = useMapStore();
const storeUser = useUserStore();
const storeBA = useBAStore();
const { getHuntingStopInfo } = useHuntingStop();

const { loading, lastLocations, groupedSilverCoins, devTools } =
  storeToRefs(storeMap);
const {
  user,
  isEnabledGPS,
  isDifferentCountry,
  seasonCode,
  crystals,
  onboarding,
  settings,
} = storeToRefs(storeUser);

const { t } = useI18n();
const { track, trackTime } = useTrackData();
const { openDialog, push, currentPath, dialogs } = useMicroRoute();
const { isAllow, requestDeviceMotion } = useMotionTracking();
const { flyTo } = useFlyTo({
  map: storeMap.mapIns,
});

const MAP_CENTERS: Record<string, LngLatLike> = {
  VN: [106.72044, 10.78156],
  SG: [103.8289643744385, 1.2486521602831857],
} as const;

const MAP_IMAGE_NAMES = [
  'sqkii',
  'silver-flag',
  'silver-flag-2',
  'golden-flag',
  'crystal-coin-flag',
  'rock',
  'boundary_dbs',
] as const;

const isTriggered = ref(false);

const idleInHome = computed(() => {
  const isHomeScreen = last(currentPath.value.split('/')) === 'home';
  const noDialog = !dialogs.value.some((d) => d.actived);
  return [isHomeScreen, noDialog].every(Boolean);
});

const mapOptions = computed<Partial<MapOptions>>(() => ({
  container: 'map',
  style: process.env.MAP_STYLE,
  minZoom: !!process.env.IS_TESTING_ENV ? 1 : 9,
  maxZoom: 22,
  zoom: 9.4,
  center: MAP_CENTERS[seasonCode.value],
}));

const images = computed(() =>
  MAP_IMAGE_NAMES.map((name) => ({
    id: name,
    image: `imgs/map/${name}.png`,
  }))
);

const { register: registerMap, mapInstance, mapStatus } = useMapbox();

watch(
  () => mapInstance.value,
  () => {
    if (!mapInstance.value) return;

    storeMap.mapIns = mapInstance.value;

    // Disable map interactions for better UX
    mapInstance.value.touchZoomRotate.disableRotation();
    mapInstance.value.doubleClickZoom.disable();
    mapInstance.value.dragRotate.disable();
    mapInstance.value.touchPitch.disable();

    // Expose map instance for testing
    if (process.env.IS_TESTING_ENV) {
      (window as any).mapInstance = mapInstance.value;
    }
  }
);

watch(mapStatus, async (status) => {
  if (status === MapCreationStatus.Loaded) {
    storeMap.loading = false;
  }
});

function handleErrorGPS() {
  storeMap.geoState = 'UNAVAILABLE';
  storeUser.setUserGPS(false);

  if (!isTriggered.value) {
    errorNotify({
      message: t('GPS_ERROR_NOT_DETECTED'),
    });
  }

  isTriggered.value = true;
  track('gps', { status: 'disabled' });
}

async function serverUpdateLocation() {
  const [lng, lat] = lastLocations.value;

  let payload: IPayloadTrackUserLocation = { lng, lat };

  if (coins.value.length > 0) {
    payload.coins = coins.value.map((coin) => ({
      coin_id: coin.properties._id,
      radius: coin.properties.circle.radius,
    }));
  }

  USER.location(payload);

  handleLocationBasedMission(lng, lat);

  return true;
}

async function handleLocationBasedMission(lng: number, lat: number) {
  const { timeMissionData } = storeBA;

  if (!timeMissionData) return;

  const isLocationBasedMission =
    timeMissionData.type === 'location_based' &&
    timeMissionData.status === 'new';

  if (!isLocationBasedMission) return;

  const nearestOutlet = storeMap.getNearestOutlet(
    timeMissionData.brand_unique_id
  );
  const isWithinRange = (nearestOutlet?.distance ?? Infinity) <= 100;

  if (!isWithinRange) return;

  try {
    const { data } = await BRAND_ACTION.updateLocationBase({ lng, lat });

    if (!data.updated) return;

    // Update mission progress and status
    if (data.updatedData.progress) {
      timeMissionData.progress = data.updatedData.progress;
    }
    if (data.updatedData.status) {
      timeMissionData.status = data.updatedData.status;
    }
  } catch (error) {
    console.error('Failed to update location-based mission:', error);
  }
}

useQuery({
  queryKey: ['serverUpdateLocation'],
  queryFn: serverUpdateLocation,
  refetchInterval: 5000,
  enabled: isEnabledGPS,
});

useQuery({
  queryKey: ['fetchRmiOutlets'],
  queryFn: () => {
    const [lng, lat] = lastLocations.value;
    storeMap.fetchRmiOutlets(lng, lat);
    return true;
  },
  refetchInterval: 5000,
  enabled: computed(() => isEnabledGPS.value && idleInHome.value),
});
const throttleUpdateLocation = throttle(storeMap.setUserLocation, 1000);

function handleSuccessGPS(geolocateEvent: GeolocateSuccess) {
  const { coords } = geolocateEvent;

  throttleUpdateLocation([coords.longitude, coords.latitude]);
  storeMap.geoState = geolocateEvent.target._watchState;
  storeUser.setUserGPS(true);

  if (!isTriggered.value) {
    showGPSNotification();
    track('gps', { status: 'enabled' });
  }

  isTriggered.value = true;
}

function showGPSNotification() {
  if (!isDifferentCountry.value) {
    successNotify({
      message: t('GPS_GPSENABLED'),
    });
  } else {
    errorNotify({
      message: t('GPS_ERROR_COUNTRY_NOT_DETECTED'),
    });
  }
}

const coins = computed(() => {
  const userPoint = point(lastLocations.value);
  return groupedSilverCoins.value.ongoing.filter((coin) =>
    booleanPointInPolygon(userPoint, coin as any)
  );
});

watch(loading, async (isLoading) => {
  if (!isLoading && isEnabledGPS.value) {
    await delay(1500);
    storeMap.triggerGPS();
  }
});

function checkLayerVisibility(feature: MapGeoJSONFeature) {
  if (!feature?.layer) return false;
  if (feature.layer.type !== 'symbol') return true;

  const symbolPaint = feature.layer.paint as SymbolLayerStyle;
  return !!symbolPaint && symbolPaint['icon-opacity'] > 0.7;
}

// function handleSponsorLayerClick(e: MapMouseEvent) {
//   const features = e.features?.filter((f) => LAYERS.hasOwnProperty(f.layer.id));

//   if (!features?.length) return;

//   const feature = features?.[0];
//   if (!feature) return;

//   const {
//     properties: iconProperties,
//     layer: { id: layerId },
//   } = feature;
//   if (!iconProperties || !checkLayerVisibility(layerId)) return;

//   const { brand_unique_id } = iconProperties;
//   if (!brand_unique_id) return;

//   if (process.env.IS_TESTING_ENV) {
//     console.log('outlet location:', JSON.parse(iconProperties.location));
//     console.log('outlet brand_unique_id', iconProperties.brand_unique_id);
//   }

//   track('tap_brand_icon', {
//     brand_id: brand_unique_id,
//     brand_icon: iconProperties.brand_icon,
//     unique_id: iconProperties.unique_id,
//   });

//   const _iconData = {
//     ...iconProperties,
//     display: JSON.parse(iconProperties.display),
//     location: JSON.parse(iconProperties.location),
//     opening_hours: JSON.parse(iconProperties.opening_hours),
//   };

//   openDialog('brand_sponsor', {
//     iconData: _iconData,
//   });
// }

// Touchable layer IDs in priority order (highest to lowest)
const TOUCHABLE_LAYER_IDS = [
  'customize_megastops1',
  'customize_huntingstop1',
  'customize_landmark1',
  'customize_rmi_m_1',
  'customize_rmi_m_2',
  'customize_rmi_m_3',
  'customize_rmi_m_4',
  'customize_rmi_m_5',
  'found_coin_symbol_layer',
  'past_found_coin_symbol_layer',
  'listener_fill_layer',
] as const;

const COIN_LAYER_IDS = [
  'found_coin_symbol_layer',
  'past_found_coin_symbol_layer',
  'listener_fill_layer',
] as const;

const RMI_LAYER_IDS = [
  'customize_rmi_m_1',
  'customize_rmi_m_2',
  'customize_rmi_m_3',
  'customize_rmi_m_4',
  'customize_rmi_m_5',
] as const;

let isClickHandling = false;
function handleLayerClick(event: MapMouseEvent) {
  if (isClickHandling || !storeMap.mapIns) return;

  isClickHandling = true;

  try {
    const highestPriorityFeature = findHighestPriorityFeature(event);
    if (!highestPriorityFeature) return;

    processFeatureClick(highestPriorityFeature);
  } finally {
    setTimeout(() => {
      isClickHandling = false;
    }, 100);
  }
}

function findHighestPriorityFeature(event: MapMouseEvent) {
  if (!storeMap.mapIns) return null;

  const features = storeMap.mapIns
    .queryRenderedFeatures(event.point)
    .filter((feature) => !feature.layer.id.startsWith('donottouch_'));

  if (!features.length) return null;

  // Find touchable features in priority order
  for (const layerId of TOUCHABLE_LAYER_IDS) {
    const layerFeatures = features.filter((f) => f.layer.id === layerId);
    if (!layerFeatures.length) continue;

    const visibleFeatures = layerFeatures.filter(checkLayerVisibility);
    if (!visibleFeatures.length) continue;

    // Return the nearest feature if multiple exist
    return findNearestFeature(visibleFeatures, event);
  }

  return null;
}

function findNearestFeature(
  features: MapGeoJSONFeature[],
  event: MapMouseEvent
) {
  return features.sort((f1, f2) => {
    // Prioritize symbol layers
    const type1 = f1.layer.type;
    const type2 = f1.layer.type;
    if (type1 === 'symbol' && type2 !== 'symbol') return -1;
    if (type1 !== 'symbol' && type2 === 'symbol') return 1;

    const f1Coords = (f1.geometry as any).coordinates;
    const f2Coords = (f2.geometry as any).coordinates;

    const d1 = event.lngLat.distanceTo({
      lng: f1Coords[0],
      lat: f1Coords[1],
    } as LngLat);
    const d2 = event.lngLat.distanceTo({
      lng: f2Coords[0],
      lat: f2Coords[1],
    } as LngLat);

    return d1 - d2;
  })[0];
}

async function processFeatureClick(feature: MapGeoJSONFeature) {
  const layerId = feature.layer.id;

  if (COIN_LAYER_IDS.includes(layerId as (typeof COIN_LAYER_IDS)[number])) {
    handleCoinAction(feature.properties as ISilverCoin);
    return;
  }

  if (RMI_LAYER_IDS.includes(layerId as (typeof RMI_LAYER_IDS)[number])) {
    console.log('RMI clicked', feature.properties);
    const id = feature.properties.id as string;
    const data = await getRmiOutletByUniqueId(id);
    if (!data) return;
    await flyTo({
      center: [data.location.lng, data.location.lat],
      zoom: 18,
      offset: [0, window.screen.availHeight * -0.25],
    });
    openDialog('rmi', { data });
    return;
  }

  switch (layerId) {
    case 'customize_megastops1':
      console.log('Mega stop clicked', feature.properties);

      const id = feature.properties.id as string;
      if (!id) return;
      const megastopData = await getHuntingStopInfo(id);
      push('hunting_stop', {
        data: megastopData,
      });
      break;
    case 'customize_huntingstop1':
      console.log('Mega stop clicked', feature.properties);
      const stop_id = feature.properties['Stop ID'] as string;
      if (!stop_id) return;
      const huntingstopData = await getHuntingStopInfo(stop_id);
      push('hunting_stop', {
        data: huntingstopData,
      });
      break;
    case 'customize_landmark1':
      console.log('Landmark clicked', feature.properties);
      const ID = feature.properties.ID as string;
      const unique_id = ID.replaceAll('/\n', '');
      const data = await getRmiOutletByUniqueId(unique_id);
      if (!data) return;
      await flyTo({
        center: [data.location.lng, data.location.lat],
        zoom: 18,
        offset: [0, window.screen.availHeight * -0.25],
      });
      openDialog('rmi_landmark', { data });
      break;
  }
}

async function getRmiOutletByUniqueId(uniqueId: string) {
  try {
    Loading.show();
    const { data } = await RMI.detail(uniqueId);
    return data;
  } catch (error) {
    const { error_message } = error as IAPIResponseError;
    errorNotify({
      message: error_message,
    });
  } finally {
    Loading.hide();
  }
}

function parseCoinProperties(rawProperties: ISilverCoin) {
  return {
    ...rawProperties,
    circle: JSON.parse(rawProperties.circle as any) as ICircle,
    winner_info: JSON.parse(rawProperties.winner_info as any) as IWinnerInfo,
    videos: JSON.parse(rawProperties.videos as any) as string[],
  };
}

function handleOngoingCoin(properties: ISilverCoin) {
  if (properties.time_up) {
    openDialog('forfeited_timeup_coin', {
      coin: properties,
      type: 'time_up',
    });
    return;
  }

  const safetyCheckKey = `coin_safety_${properties._id}_${user.value?.hunter_id}`;
  const isChecked = LocalStorage.getItem(safetyCheckKey);

  if (
    !isChecked ||
    isDifferentDay(isChecked.toString(), new Date().toISOString())
  ) {
    openDialog('coin_extra_safety', {
      coin: properties,
    });
    return;
  }

  openDialog('silver_coin', {
    coin: {
      ...properties,
      freeCircle: JSON.parse(properties.freeCircle as any) as ICircle,
      paidCircle: JSON.parse(properties.paidCircle as any) as ICircle,
    },
  });
}

function handleCoinAction(coinFeature: ISilverCoin) {
  const properties = parseCoinProperties(coinFeature) as ISilverCoin;
  const handlers: Record<TCoinStatus, () => void> = {
    ongoing: () => handleOngoingCoin(properties),
    verifying: () => handleVerifyingCoin(properties),
    forfeited: () => handleForfeitedCoin(properties),
    found: () => handleFoundCoin(properties),
  };

  const handler = handlers[properties.status];
  if (handler) handler();

  // switch (properties.status) {
  //   case 'ongoing':
  //     await handleOngoingCoin(properties);
  //     break;
  //   case 'verifying':
  //     handleVerifyingCoin(properties);
  //     break;
  //   case 'forfeited':
  //     handleForfeitedCoin(properties);
  //     break;
  //   case 'found':
  //     handleFoundCoin(properties);
  //     break;
  // }

  handleCoinAnalytics(properties);
}

function handleVerifyingCoin(properties: ISilverCoin) {
  track('silvercoin_map_verifying', {
    action: 'silvercoin_map_verifying_circle',
  });

  if (user.value?.id === properties.user) {
    openDialog('silver_coin_founder', {
      coin: properties,
      fromMap: true,
    });
  } else {
    openDialog('coin_verification_vide', {
      coin: properties,
    });
  }
}

function handleForfeitedCoin(properties: ISilverCoin) {
  track('silvercoin_map_forfeited', {
    action: 'silvercoin_map_forfeited_circle',
  });

  openDialog('forfeited_timeup_coin', {
    coin: properties,
    type: 'forfeited',
  });
}

function handleFoundCoin(properties: ISilverCoin) {
  if (properties.type === 'golden') {
    handleGoldenCoin();
    return;
  }

  handleSilverFoundCoin(properties);
}

function handleGoldenCoin() {
  const coin = { ...settings.value?.golden_coin };

  openDialog('found_coin', {
    type: 'golden',
    coin,
  });

  track('golden-flag');
}

function handleSilverFoundCoin(properties: ISilverCoin) {
  openDialog('found_coin', {
    type: 'silver',
    coin: properties,
  });

  track('silvercoin_foundflag', {
    action: 'silvercoin_foundflag',
  });

  track('flag', {
    coin_id: properties._id,
  });
}

function handleCoinAnalytics(properties: ISilverCoin) {
  if (properties.geohash) return;

  trackTime('start_session');

  if (properties.status === 'found') return;

  const circle = properties.circle;

  if (!onboarding.value?.shrink_silver_coin) {
    track('onboarding_silvercircle');
  }

  track('tap_coin_circle', {
    brand_unique_id: properties.brand_unique_id,
    coin_id: properties._id,
    hunter_id: user.value?.hunter_id,
    user_crystals: crystals.value,
    radius: circle.radius ?? 0,
  });
}

function handleClick(event: Event) {
  const target = event.target as HTMLElement;

  const clickHandlers: Record<string, () => void> = {
    upcoming: () => openDialog('time_line_v2'),
    'reminder-gps': () => {
      openDialog('gps');
      closeNotify();
    },
    'reminder-login': () => openDialog('login'),
    'reminder-signup': () => openDialog('signup'),
  };

  const handler = clickHandlers[target.id];
  if (handler) handler();
}

function handleStyleMissing(event: Event): void {
  console.log('style image missing', event);
}

watch(
  isAllow,
  (isAllowed) => {
    if (isAllowed) {
      requestDeviceMotion();
    }
  },
  { immediate: true }
);

onMounted(async () => {
  await nextTick();

  await Promise.all([
    storeBA.fetchBrandAction(),
    storeMap.fetchMapGeoHashes(),
    storeMap.fetchSilverCoin(),
    storeMap.fetchBrandIcon(),
    storeMap.fetchMetalSonar(),
  ]);
  // trialSequenceMessage();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <Mapbox
    :options="mapOptions"
    @register="registerMap"
    @zoomend="storeMap.setZoom"
    debug
    @styleimagemissing="handleStyleMissing"
    @click="handleLayerClick"
  >
    <template v-if="mapStatus === MapCreationStatus.Loaded">
      <GeolocateControls
        :options="{
          positionOptions: {
            enableHighAccuracy: true,
          },
          trackUserLocation: true,
          showAccuracyCircle: true,
        }"
        @geolocate="handleSuccessGPS"
        @error="handleErrorGPS"
        @trackuserlocationend="storeMap.setGeoState"
        @trackuserlocationstart="storeMap.setGeoState"
        @register="storeMap.setGeoIns"
      />

      <Image :images="images" />

      <template v-if="seasonCode === 'SG'">
        <BoundaryLayer />
      </template>

      <TimeUpCoinLayer />
      <ForfeitedCoinLayer />
      <VerifyingCoinLayer />
      <OngoingFreeCoinLayer />
      <OngoingPaidCoinLayer />
      <CoinSonarLayer />
      <ListenerCoinLayer @click="handleLayerClick" />
      <FoundCoinLayer @click="handleLayerClick" />
      <BeaconLayer />
      <VerifyingPopUp />
      <ForfeitedPopUp />
      <PaidPopUp />
      <TimeUpPopUp />
      <FoundPopUp />
      <BeaconPopUp />
      <CoinSonarPopUp />

      <template v-if="devTools.fakeGps">
        <DevToolLocation />
      </template>

      <LocationBasedMission />

      <!-- <SponsorLayer @click="handleSponsorLayerClick" /> -->

      <PivotLayer pivot-id="beacon_pivot" :target-layer-ids="[]" />

      <PivotLayer
        pivot-id="smrt_station_pivot"
        :target-layer-ids="['htm_sg_outlets_symbol_layer']"
      />
    </template>
  </Mapbox>
</template>
<style lang="scss">
#maplibre_container {
  width: 100%;
  height: 100%;
}
</style>
