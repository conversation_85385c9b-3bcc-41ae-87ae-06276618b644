<template>
  <div class="fullscreen stall" :class="{ mega: type === 'mega' }">
    <div
      class="min-h-[64px] w-full flex flex-center text-center absolute top-0 z-20"
    >
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left"></Icon>
      </Button>
      <div class="location-name" :class="{ mega: type === 'mega' }">
        {{ name }}
      </div>
    </div>
    <div
      class="banner text-center p-[18px] opacity-0"
      :class="{
        error: alertMessage.type === 'error',
        'pointer-events-none': !alertMessage.action,
      }"
      v-html="alertMessage.message"
      @click="alertMessage.action"
    ></div>
    <div
      class="speech opacity-0"
      v-html="dialogMessage.message"
      @click="dialogMessage.action"
    ></div>
    <canvas
      class="w-full h-full absolute bottom-0 left-0 z-10"
      ref="stop"
      v-show="!hasError"
      @click="
        gsap.killTweensOf('.tap');
        gsap.set('.tap', { display: 'none' });
      "
    >
    </canvas>
    <img
      src="/imgs/huntingstop/tap.png"
      class="tap opacity-0 pointer-events-none left-1/2 fixed bottom-[50vw] -translate-x-1/2 -translate-y-1/2 w-24 h-auto z-20"
      alt=""
    />
  </div>
</template>

<script setup lang="ts">
import { IRiveViewModel, useHuntingStop, useRive } from '@composables';
import { timeCountDown } from '@helpers';
import { Fit, Alignment, Layout } from '@rive-app/canvas';
import { HuntingStop } from '@types';
const { push } = useMicroRoute();
const { claimReward: claimRewardFn, stallError } = useHuntingStop();
const { t } = useI18n();
import gsap from 'gsap';

const stop = ref<HTMLCanvasElement | null>(null);

const claimReward = ref(false);

const props = defineProps<HuntingStop>();

const isRewardRevealed = ref(!!props.lock_until && !!props.rewards);

const lockUntil = toRefs(props).lock_until;

const vmConfig = computed<IRiveViewModel[]>(() =>
  !!props.lock_until && !!props.rewards
    ? [
        {
          path: 'selectCharacter',
          type: 'enum',
          value: props.type === 'mega' ? 'nancii' : 'sqkii',
        },
        {
          path: 'isDoorOpen',
          type: 'boolean',
          value: true,
        },
        // {
        //   path: 'numItem',
        //   type: 'number',
        //   value: Object.keys(props.rewards || {}).length,
        // },
        {
          path: 'property of vmCharacter/tickleCounter',
          type: 'number',
          value: 15,
        },
        {
          path: 'property of vmCharacter/beingTickled',
          type: 'boolean',
          value: true,
        },
        ...Object.keys(props.rewards || {})
          .map((key, idx) => [
            {
              path: `vmItem${idx + 1}/itemSelected`,
              type: 'enum',
              value: key.replace(/\_/gim, ' ').toLowerCase(),
            },
            {
              path: `vmItem${idx + 1}/itemCounter`,
              type: 'number',
              value: (props.rewards || {})[key as RewardType],
            },
            {
              path: `vmItem${idx + 1}/itemClicked`,
              type: 'trigger',
              value: rewardDetail,
            },
          ])
          .flat(),
      ]
    : [
        {
          path: 'selectCharacter',
          type: 'enum',
          value: props.type === 'mega' ? 'nancii' : 'sqkii',
        },
        {
          path: 'isDoorOpen',
          type: 'boolean',
          value: () => {
            isRewardRevealed.value = true;
            props.fun_facts && showDialogMessage(props.fun_facts);
            if (lockUntil.value) {
              const delta = timeCountDown(
                +new Date(lockUntil.value) - Date.now()
              );
              showAlertMessage(
                t('huntingstop.next_reward', {
                  time: delta,
                }),
                () => push(-1),
                'info'
              );
            }
          },
        },
        {
          path: 'property of vmCharacter/tap',
          type: 'trigger',
          value: () => {
            if (isRewardRevealed.value && lockUntil.value) {
              const delta = timeCountDown(
                +new Date(lockUntil.value) - Date.now()
              );
              showAlertMessage(
                t('huntingstop.next_reward', {
                  time: delta,
                }),
                () => push(-1),
                'info'
              );
            } else {
              hideDialogMessage();
            }
          },
        },
        {
          path: 'property of vmCharacter/tickleCounter',
          type: 'number',
          value: async (val: number) => {
            if (val > 5 && !claimReward.value) {
              claimReward.value = true;
              const { rewards, lock_until } = await claimRewardFn(
                props.unique_id
              );
              lockUntil.value = lock_until;

              // bindViewModelInstance([
              //   {
              //     path: 'numItem',
              //     type: 'number',
              //     value: Object.keys(rewards).length,
              //   },
              //   ...Object.keys(rewards)
              //     .map(
              //       (key, idx) =>
              //         [
              //           {
              //             path: `vmItem${idx + 1}/itemSelected`,
              //             type: 'enum',
              //             value: key.replace(/\_/gim, ' ').toLowerCase(),
              //           },
              //           {
              //             path: `vmItem${idx + 1}/itemCounter`,
              //             type: 'number',
              //             value: rewards[key as RewardType],
              //           },
              //           {
              //             path: `vmItem${idx + 1}/itemClicked`,
              //             type: 'trigger',
              //             value: rewardDetail,
              //           },
              //         ] as IRiveViewModel[]
              //     )
              //     .flat(),
              // ]);
            }
          },
        },
      ]
);

function rewardDetail(type: RewardType) {
  console.log('Reward Type:', type);
}

function onRiveloaded() {
  if (hasError.value) {
    return;
  }
  gsap.to('.tap', {
    opacity: 1,
    duration: 1,
    delay: 8,
  });

  !!props.fun_facts && showDialogMessage(props.fun_facts);

  if (props.lock_until) {
    const delta = timeCountDown(+new Date(props.lock_until) - Date.now());
    showAlertMessage(
      t('huntingstop.next_reward', {
        time: delta,
      }),
      () => push(-1),
      'info'
    );
  }
}
const { bindViewModelInstance } = useRive({
  canvas: stop as Ref<HTMLCanvasElement | null>,
  src: 'imgs/huntingstop/hunting_stop.riv',
  autoplay: true,
  autoBind: false,
  stateMachines: 'State Machine',
  layout: new Layout({
    fit: Fit.FitWidth,
    alignment: Alignment.BottomCenter,
  }),
  onLoad: onRiveloaded,
  data: vmConfig.value,
});

const dialogMessage = ref<{
  message: string;
  action?: () => void;
}>({
  message: '',
  action: void 0,
});

const alertMessage = ref<{
  message: string;
  action?: () => void;
  type?: 'error' | 'warning' | 'info';
}>({
  message: '',
});
function showAlertMessage(
  message: string,
  action?: () => void,
  type: 'error' | 'warning' | 'info' = 'info'
) {
  alertMessage.value = { message, action, type };
  gsap.fromTo(
    '.banner',
    { opacity: 0, y: -15 },
    {
      opacity: 1,
      y: 0,
      duration: 1,
    }
  );
}
function showDialogMessage(message: string, action?: () => void) {
  dialogMessage.value = { message, action };
  gsap.fromTo(
    '.speech',
    { opacity: 0, y: 5 },
    {
      opacity: 1,
      y: 0,
      duration: 1,
    }
  );
}
function hideAlertMessage() {
  gsap.to('.banner', {
    opacity: 0,
    y: -15,
    duration: 1,
    onComplete: () => {
      alertMessage.value = { message: '' };
    },
  });
}
function hideDialogMessage() {
  gsap.to('.speech', {
    opacity: 0,
    y: 5,
    duration: 1,
  });
}

const hasError = computed(() => {
  return stallError.value.values().some(Boolean);
});

onMounted(async () => {
  await nextTick();
  switch (true) {
    case stallError.value.get('gps_disabled'):
      showAlertMessage(t('huntingstop.gps_disabled'), () => push(-1), 'error');
      break;
    case stallError.value.get('need_verify'):
      showAlertMessage(t('huntingstop.need_verify'), () => push(-1), 'error');
      break;
    case stallError.value.get('speed_limit'):
      showAlertMessage(t('huntingstop.speed_limit'), () => push(-1), 'error');
      break;
    case stallError.value.get('reached_daily_limit'):
      showAlertMessage(
        t('huntingstop.reached_daily_limit'),
        () => push(-1),
        'error'
      );
      break;
    default:
      break;
  }
});

onBeforeUnmount(() => {
  gsap.killTweensOf('.tap');
  gsap.killTweensOf('.banner');
  gsap.killTweensOf('.speech');
});
</script>

<style lang="scss" scoped>
.stall {
  background: url('/imgs/huntingstop/stall-bg.png') no-repeat center center,
    linear-gradient(180deg, #3c1526 8.46%, #8c1946 94.96%), #d9d9d9;
  background-size: 130% auto;

  &.mega {
    background: url('/imgs/huntingstop/stall-bg-mega.png') no-repeat center
        center,
      linear-gradient(180deg, #3c1526 8.46%, #8c1946 94.96%), #d9d9d9;
    background-size: cover;
  }

  &::before {
    width: 126%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::before {
    width: 100%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top-mega.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &::after {
    width: 100%;
    position: absolute;
    height: 100vw;
    background: url('/imgs/huntingstop/stall-bot.png') no-repeat top center;
    background-size: 100% 110%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::after {
    width: 100%;
    position: absolute;
    height: 80vw;
    background: url('/imgs/huntingstop/stall-bot-mega.png') no-repeat top center;
    background-size: 110% 110%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .speech {
    position: absolute;
    bottom: 115vw;
    left: 50%;
    transform: translateX(-50%);
    width: 90vw;
    min-height: 22.26vw;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 12px;
    padding-bottom: calc(12px + 4vw);

    background: url('/imgs/huntingstop/speech.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 20;
  }

  .location-name {
    border-radius: 8px;
    border: 3px solid #e2b595;
    background: linear-gradient(198deg, #bb7d54 28.64%, #88502b 119.57%);
    padding: 10px 60px;
    font-size: 16px;
    font-weight: 700;

    &.mega {
      background: url('/imgs/huntingstop/banner.png') no-repeat center center;
      background-size: 100% 100%;
      border: none;
      color: black;
      height: 54px;
      min-width: 280px;
      text-align: center;
      padding: 12px 24px 18px;
      transform: translateY(30px);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, -100%);
        width: 90%;
        height: 40px;
        background: url('/imgs/huntingstop/chain.png') repeat-y bottom center;
        background-size: 100% auto;
      }
    }
  }

  .banner {
    position: absolute;
    top: 90px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    min-height: 64px;
    background: url('/imgs/huntingstop/alert.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 20;

    &.error {
      background: url('/imgs/huntingstop/error-alert.png') no-repeat center
        center;
      background-size: 100% 100%;
    }
  }
}
</style>
