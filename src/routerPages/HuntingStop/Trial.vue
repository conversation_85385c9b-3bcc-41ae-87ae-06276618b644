<template>
  <div
    class="fullscreen stall"
    :class="{ 'mega': type === 'mega' }"
  >
    <div class="min-h-[64px] w-full flex flex-center text-center absolute top-0 z-20">
      <Button
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="
          push(-1);
        "
      >
        <Icon name="arrow-left"></Icon>
      </Button>
      <div
        class="location-name"
        :class="{ 'mega': type === 'mega' }"
      >{{ name }}</div>
    </div>
    <div
      class="banner text-center p-[18px] opacity-0"
      :class="{ 'error': alertMessage.type === 'error', 'pointer-events-none': !alertMessage.action }"
      v-html="alertMessage.message"
      @click="alertMessage.action"
    >
    </div>
    <div
      class="speech opacity-0"
      v-html="dialogMessage.message"
      @click="dialogMessage.action"
    >


    </div>
    <canvas
      class="w-full h-full absolute bottom-0 left-0 z-10"
      ref="stop"
      :class="{ 'pointer-events-none': !isEnabledGPS }"
      @click="gsap.killTweensOf('.tap'); gsap.set('.tap', { display: 'none' })"
    >

    </canvas>
    <img
      src="/imgs/huntingstop/tap.png"
      class="tap opacity-0 pointer-events-none left-1/2 fixed bottom-[50vw] -translate-x-1/2 -translate-y-1/2 w-24 h-auto z-20"
      alt=""
    >
  </div>
</template>


<script setup lang="ts">
import { HuntingStop } from "@repositories/huntingstop";
import { Rive, Fit, Alignment, Layout } from "@rive-app/canvas";
import { useUserStore } from "@stores";
import gsap from "gsap";

const { push } = useMicroRoute()
const props = defineProps<HuntingStop>();
const stop = ref<HTMLCanvasElement | null>(null);
const storeUser = useUserStore()
const { isEnabledGPS } = storeToRefs(storeUser);


const vmConfig = {
  normal: {
    path: "selectCharacter",
    type: 'enum',
    value: 'sqkii'
  },
  mega:
  {
    path: "selectCharacter",
    type: 'enum',
    value: 'nanci'
  }

}

const dialogMessage = ref<{
  message: string,
  action?: () => void
}>({
  message: '',
  action: void 0
})

const alertMessage = ref<{
  message: string,
  action?: () => void,
  type?: 'error' | 'warning' | 'info'
}>({
  message: '',
})

function showAlertMessage(message: string, action?: () => void, type: 'error' | 'warning' | 'info' = 'info') {
  alertMessage.value = { message, action, type }
  gsap.fromTo('.banner', { opacity: 0, y: -15 }, {
    opacity: 1,
    y: 0,
    duration: 1
  });
}
function showDialogMessage(message: string, action?: () => void) {
  dialogMessage.value = { message, action }
  gsap.fromTo('.speech', { opacity: 0, y: 5 }, {
    opacity: 1,
    y: 0,
    duration: 1,
  });
}
function hideAlertMessage() {
  gsap.to('.banner', {
    opacity: 0,
    y: -15,
    duration: 1,
    onComplete: () => {
      alertMessage.value = { message: '' }
    }
  });
}
function hideDialogMessage() {
  gsap.to('.speech', {
    opacity: 0,
    y: 5,
    duration: 1,
  });
}
const isRewardRevealed = ref(false);


onMounted(async () => {
  await nextTick()

  showAlertMessage('Special visit!')
  if (!isEnabledGPS.value) {
    showDialogMessage(
      '<div>Welcome to my Huntingstop! You’ll need to <span  class="text-[#00E0FF] font-bold underline">enable your GPS</span> to get rewards here.</div>',
      () => push(-1),
    )
  }
  else showDialogMessage(
    props.type === 'mega' ?
      'Welcome to my Megastop! Tap to tickle me and get rewards!'
      : 'Welcome to my Huntingstop! Tap to tickle me and get rewards!'
  );

  if (stop.value) {
    const rive = new Rive({
      src: 'imgs/huntingstop/hunting_stop.riv',
      canvas: stop.value,
      stateMachines: "State Machine",
      autoplay: true,
      autoBind: false,
      automaticallyHandleEvents: false,
      layout: new Layout({
        fit: Fit.FitWidth,
        alignment: Alignment.BottomCenter,
      }),
      onLoad: () => {
        rive.resizeDrawingSurfaceToCanvas();
        gsap.to('.tap', {
          opacity: 1,
          duration: 1,
          delay: 8,
        })
        const vm = rive.defaultViewModel()
        const vmi = vm?.defaultInstance()
        if (!vmi) {
          console.error('No ViewModelInstance found');
          return;
        }
        const charConf = vmConfig[props.type]
        vmi.enum(charConf.path)!.value = charConf.value

        vmi.boolean('isDoorOpen')?.on(_ => {
          isRewardRevealed.value = true
          showDialogMessage(
            props.type === 'normal'
              ? `That was fun! Explore other Huntingstops or check out the brand action below for more rewards.`
              : `That was fun! Explore other Megastops for more rewards, or do the brand action below!`,
          );
          if (
            props.type === 'mega'
          ) {
            storeUser.updateOnboarding('trial_hunting_stop_mega');
          } else {
            storeUser.updateOnboarding('trial_hunting_stop_normal');
          }
        })
        vmi.viewModel("property of vmCharacter")?.trigger('tap')?.on(_ => {
          if (isRewardRevealed.value) {
            showAlertMessage(
              '30m 00s to next reward',
            );
          } else {
            hideDialogMessage()
          }
        });

      [].forEach((reward, index) => {
          const vmiItem = vmi.viewModel(`vmItem${index + 1}`);
          if (!vmiItem) return;
          vmiItem.enum('itemSelected')!.value = reward.name;
          vmiItem.number("itemCounter")!.value = reward.qty;
          vmi.trigger(`clickItem${index + 1}`)?.on(reward.onClick);
        });
        rive.bindViewModelInstance(vmi)

      },
    });
  }
});
onBeforeUnmount(() => {
  gsap.killTweensOf('.tap');
  gsap.killTweensOf('.banner');
  gsap.killTweensOf('.speech');
});

</script>


<style lang="scss" scoped>
.stall {
  background: url('/imgs/huntingstop/stall-bg.png') no-repeat center center, linear-gradient(180deg, #3C1526 8.46%, #8C1946 94.96%), #D9D9D9;
  background-size: 130% auto;

  &.mega {
    background: url('/imgs/huntingstop/stall-bg-mega.png') no-repeat center center, linear-gradient(180deg, #3C1526 8.46%, #8C1946 94.96%), #D9D9D9;
    background-size: cover;
  }

  &::before {
    width: 126%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &.mega::before {
    width: 100%;
    position: absolute;
    height: 100%;
    background: url('/imgs/huntingstop/stall-top-mega.png') no-repeat top center;
    background-size: 100% auto;
    content: '';
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  &::after {
    width: 100%;
    position: absolute;
    height: 100vw;
    background: url('/imgs/huntingstop/stall-bot.png') no-repeat top center;
    background-size: 100% 110%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }


  &.mega::after {
    width: 100%;
    position: absolute;
    height: 80vw;
    background: url('/imgs/huntingstop/stall-bot-mega.png') no-repeat top center;
    background-size: 110% 110%;
    content: '';
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }

  .speech {
    position: absolute;
    bottom: 120vw;
    left: 50%;
    transform: translateX(-50%);
    width: 90vw;
    min-height: 22.26vw;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 12px;
    padding-bottom: calc(12px + 4vw);

    background: url('/imgs/huntingstop/speech.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 20;
  }

  .location-name {
    border-radius: 8px;
    border: 3px solid #E2B595;
    background: linear-gradient(198deg, #BB7D54 28.64%, #88502B 119.57%);
    padding: 10px 60px;
    font-size: 16px;
    font-weight: 700;

    &.mega {
      background: url('/imgs/huntingstop/banner.png') no-repeat center center;
      background-size: 100% 100%;
      border: none;
      color: black;
      height: 54px;
      min-width: 280px;
      text-align: center;
      padding: 12px 24px 18px;
      transform: translateY(30px);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, -100%);
        width: 90%;
        height: 40px;
        background: url('/imgs/huntingstop/chain.png') repeat-y bottom center;
        background-size: 100% auto;
      }
    }
  }

  .banner {
    position: absolute;
    top: 90px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    min-height: 64px;
    background: url('/imgs/huntingstop/alert.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 20;

    &.error {
      background: url('/imgs/huntingstop/error-alert.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
}
</style>
