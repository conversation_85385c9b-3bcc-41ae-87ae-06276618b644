import type { ISeason, ISeasonFeatures, ComponentId } from '@types';

export interface IPayloadLogin {
  mobile_number: string;
  password: string;
}

export interface IPayloadSignup extends IPayloadLogin {
  otp: string;
}

export interface IPayloadResendOTP {
  mobile_number: string;
  type?: 'register' | 'forgot_password';
  captcha_token: string;
}

export type IUserLang = 'en' | 'vi' | 'ja';

export type ILanguages = Record<string, { label: string; value: IUserLang }>;

export interface IUser {
  expire_at: string;
  next_otp_at: string;
  lang: IUserLang;
  id: string;
  hunter_id: string;
  onboarding: Record<TOnboarding, boolean>;
  setting: IUserSetting;
  referral_code: string;
  resources: {
    crystal: number;
    beacon: number;
    dbs_crystal: number;
    sentosa_crystal_detector: number;
  };
  hunting_stop_claimed: number;
  hunting_stop_reached_limit: boolean;
  coin_drop: string[];
  metadata?: Record<string, any>;
  created_at: string;
  registered_at: string;
  mobile_number: string;
  survey?: boolean;
  end_survey?: boolean;
  change_hunter_id_attempts?: number;
  shrink_cooldown: Record<string, string>;
  verify_coin_attempts?: number;
  verify_coin_lock_until?: string;
  tiger_permission_allowed?: boolean;
  free_eliminate?: boolean;
  free_hint?: boolean;
  silver_coin_found?: number;
  claimed_welcome_reward: boolean;
  last_claim_bonus: string;
  claimed_daily_reward: string[];
  untame_account: {
    synced_at: string;
    uid: string;
    untame_id: string;
  };
  verified_mobile_number_at: string;
  coin_lock_until: string;
  session_number: number;
  metal_detector_cooldown: string;
  total_capitaland_lynden_woods_coins_submissions: number;
  total_capitaland_science_park_coins_submissions: number;
  total_coin_submissions: number;
  total_gold_coin_submissions: number;
  sv_token: string;
}

export type IViableMapLayer = 'all' | 'golden' | 'silver';

export interface IUserSetting {
  background_music: number;
  sound_effect: number;
  viable_map_layer: IViableMapLayer;
  pedometer_goal: number;
  pedometer: boolean;
}

export type TOnboarding =
  | 'first_entry'
  | 'promo_code'
  | 'tap_timii'
  | 'golden_coin_verifying'
  | 'golden_coin_found'
  | 'left_10_grids'
  | 'all_coins_found'
  | 'gold_coin_available'
  | 'during_sudden_death'
  | 'before_sudden_death'
  | 'gps'
  | 'survey_trigger'
  | 'first_sqkii_vouchers'
  | 'onboarding_journey'
  | 'shrink_silver_coin'
  | 'first_beacon'
  | 'crystal_expiring'
  | 'first_metal_detector'
  | 'first_metal_sonar'
  | 'first_crystal_detector'
  | 'first_time_claim_crystal_detector'
  | 'capitaland_onboarding_journey'
  | 'first_inventory_quick_view'
  | 'first_inventory'
  | 'first_beacon_trial'
  | 'first_trial_coin_sonar'
  | 'first_trial_metal_detector'
  | 'first_trial_silver_shrink'
  | 'trial_hunting_stop_normal'
  | 'trial_hunting_stop_mega';

export interface IChangeHunterIdFee {
  attempt?: number;
  amount?: number;
}
export interface ISettings {
  eliminate_grid_price: number;
  season_start_at: string;
  shrink_circle_price: number;
  text_hint_price: number;
  change_hunt_id_fee: IChangeHunterIdFee[];
  survey_crystal_reward: number;
  referral_reward: number;
  next_eliminate_at: string;
  grids_per_elimination: number;
  panda_mart_ba: string;
  gps_off_ba: string;
  holding_page_target_url: string;
  holding_page_state:
    | 'none'
    | 'silver'
    | 'gold'
    | 'gold_ongoing'
    | 'silver_ongoing';
  zalo_daily_quota: {
    remainingQuota: number;
    dailyQuota: 500;
  };
  golden_coin: {
    status: 'ongoing' | 'verifying' | 'found' | 'scheduled';
    start_verify_at: string;
    found_at: string;
    videos: string[];
    winner_name: string;
    geohash: string;
    location: {
      lat: number;
      lng: number;
    };
  };
  dbs: {
    start_at: string;
  };
  ms_cooldown_to_lock: number;
  ongoing_coin_remaining: number;
  silver_found_dialog: boolean;
  gold_found_dialog: boolean;
  show_fake_circles: boolean;
  frontend_build: {
    version: string;
    last_update: string;
  };
  seasons: ISeason[];
  dates: {
    season_start_at: string;
    blocker_countdown_start_at: string;
    sentosa_drop_hint_at: string;
    sentosa_beach_station_event_start_at: string;
    sentosa_beach_station_event_end_at: string;
    start_state_1: string;
    start_state_2: string;
    start_state_3: string;
    start_state_4: string;
    end_state_1: string;
    end_state_2: string;
    end_state_3: string;
    end_state_4: string;
  };
  hardcoded_contest_unique_id: string;
  supported_languages: string[];
  metal_sonar?: {
    start_at: string;
    end_at: string;
    radius: number[];
  };
  beacon: {
    radius: number;
  };
  metal_detector: {
    active_at: string;
    inactive_at: string;
    duration_in_sec: number;
    extend_duration_in_sec: number;
    max_speed: number;
    max_speed_warning_times: number;
    extend_offer_ttl: number;
  };
  features: ISeasonFeatures;
  flags: {
    last_10_grids: boolean;
    all_coins_found: boolean;
    perpetual_hunt: boolean;
    end_game_survey?: boolean;
    crystal_coin_end?: boolean;
    sentosa_coin_end?: boolean;
    capita_land_coin_end?: boolean;
    hide_beach_station_map_icon: false;
  };
  capita_land: {
    geojson_file: string;
  };
  brand_action: IBASettings;
  sentosa: {
    crystal_detector: {
      duration_in_sec: number;
      max_speed: number;
      max_speed_warning_times: number;
    };
    beach_station_hint_2_required: number;
    island_bounty: {
      start_at: string;
      end_at: string;
    };
  };
  next_silver_drop_at: string;
}

export interface IBASettings {
  multiplier: {
    featured: number;
    first_time: number;
  };
}

export interface ITimeline {
  _id: string;
  status: 'ongoing' | 'future' | 'ended';
  video_links?: string[];
  registered_at?: string;
  background_image: string;
  time: string;
  copy_position_5: string;
  copy_position_4: string;
  city: string;
  copy_position_2: string;
  country_code: string;
  copy_position_1: string;
  copy_position_3: string;
  order: number;
  hunt_name: string;
  season_id: string;
  logo: string;
  adventure_log_content: string;
  adventure_log_date: string;
  adventure_log_location: { lat: number; lng: number };
  adventure_log_title: string;
}

export interface IAdventureLog {
  state: 'ended' | 'ongoing' | 'future';
  key: string;
  content: string;
  location: {
    lng: number;
    lat: number;
  };
  order_number: number;
}

export interface IReferral {
  id: string;
  referee: {
    hunter_id: string;
    _id: string;
    mobile_number: string;
    verified_mobile_number_at: string;
  };
  status: 'claimed' | 'pending' | 'verified';
}

export interface IDailyMission {
  name: string;
  progress: number;
  required: number;
  reward: number;
  type: string;
  unique_id: string;
  claimed_at: string;
  toggle: boolean;
}

export interface ISurveyAnswer {
  value: string | number;
  size?: string;
  total?: number;
  selected?: boolean;
  title?: string;
  type?: 'area' | 'select' | 'image';
  image?: string;
  active?: boolean;
}

export interface ISurveyCondition {
  next?: (currentData: ISurveyData) => number | string;
}

export interface ISurveyData {
  id: number;
  q: string;
  sub_q: string;
  a: ISurveyAnswer[];
  type: 'select' | 'rate' | 'area' | 'slide';
  min_rate_text?: string;
  max_rate_text?: string;
  multiple?: boolean;
  condition?: ISurveyCondition;
  slide?: {
    step: number;
    unit: string;
  };
  selected?: any;
}

export interface IUserSettingPayload {
  type:
    | 'background_music'
    | 'sound_effect'
    | 'viable_map_layer'
    | 'pedometer_goal'
    | 'pedometer';
  value: unknown;
}
type PedometerDailyProgress = {
  weekday: 'Mon' | 'Tue' | 'Wed' | 'Thu' | 'Fri' | 'Sat' | 'Sun';
  date: string;
  steps: number;
  distance: number;
};
export type PedometerProgress = Array<PedometerDailyProgress>;
export interface IWalkApiResponse {
  walk_mission: IDailyMission;
  pedometer_progress: PedometerProgress;
}

export type TransactionReason =
  | 'use_shrink_power_up'
  | 'brand_action'
  | 'admin'
  | 'eliminate_pu'
  | 'buy_text_hint'
  | 'referral'
  | 'change_hunter_id'
  | 'survey'
  | 'welcome_reward'
  | 'online_time_bonus'
  | 'daily_reward'
  | 'daily_mission'
  | 'untame_spent'
  | 'brand_action_milestone'
  | 'vote_contest'
  | 'redeem_code'
  | 'use_metal_sonar'
  | 'use_metal_detector'
  | 'sqkii_voucher'
  | 'skip_mission'
  | 'timed_mission'
  | 'sentosa_daily_reward'
  | 'sentosa_island_bounty'
  | 'capitaland_amenities_check_in'
  | 'capitaland_daily_reward';

export interface ITransaction {
  amount: number;
  created_at: string;
  metadata: Record<string, unknown>;
  mission_unique_id: string;
  reason: TransactionReason;
  resource_type: string;
}

export interface ICrystalExpiring {
  date: string;
  expiring_amount: number;
}

export interface IBrandSovSettings {
  brands: Record<string, string>;
  probs: Record<ComponentId, Record<string, number>>;
  asset_packs: Record<string, string>;
}

export interface ISVSubmitReferralPayload {
  name: string;
  email: string;
  mobile_number: string;
  name_of_business: string;
}
